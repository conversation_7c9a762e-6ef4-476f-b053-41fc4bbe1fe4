{"name": "@topwrite/cli", "version": "1.0.90", "author": "yunwuxin <<EMAIL>> (https://github.com/yunwuxin)", "files": ["bin", "templates"], "scripts": {"build": "rollup -c --environment NODE_ENV:production", "build:dev": "rollup -c", "watch": "rollup -c -w", "prepack": "npm run build"}, "dependencies": {"@topwrite/reader": "^1.0.82", "archiver": "^5.3.0", "canvas": "^3.2.0", "commander": "^8.1.0", "consola": "^2.15.3", "cross-spawn": "^7.0.3", "ejs": "^3.1.6", "fs-extra": "^10.0.0", "fstream-ignore": "^1.0.5", "js-yaml": "^4.1.0", "jsdom": "^18.1.1", "lodash": "^4.17.21", "lookpath": "^1.2.0", "mime-types": "^2.1.34", "pinyin-pro": "^3.10.1", "puppeteer-core": "^23.5.3", "react": "^18", "react-dom": "^18", "readdirp": "^3.5.0", "replace-ext": "^2.0.0", "tar-pack": "^3.4.1", "tmp": "^0.2.1", "vm2": "^3.9.3"}, "peerDependencies": {"typescript": "^4.2.3"}, "devDependencies": {"@babel/core": "^7.12.10", "@babel/plugin-transform-runtime": "^7.11.5", "@rollup/plugin-babel": "^5.2.2", "@rollup/plugin-commonjs": "^21.0.1", "@rollup/plugin-json": "^4.1.0", "@rollup/plugin-node-resolve": "^13.0.0", "@topwrite/core": "^1.0.43", "@types/archiver": "^5.1.1", "@types/configstore": "^5.0.0", "@types/cross-spawn": "^6.0.2", "@types/ejs": "^3.0.6", "@types/fs-extra": "^9.0.4", "@types/js-yaml": "^4.0.5", "@types/jsdom": "^16.2.10", "@types/mime-types": "^2.1.1", "@types/node": "^24.3.0", "@types/tmp": "^0.2.0", "configstore": "^6.0.0", "dargs": "^8.0.0", "filenamify": "^5.0.1", "mdast-util-to-markdown": "^1.1.1", "p-map": "^5.3.0", "rehype-parse": "^8.0.2", "rehype-remark": "^9.1.0", "remark-gfm": "^3.0.1", "remark-stringify": "^10.0.0", "rollup": "^2.26.11", "rollup-plugin-preserve-shebangs": "^0.2.0", "rollup-plugin-terser": "^7.0.2", "rollup-plugin-typescript2": "^0.31.1", "typescript": "^5.3.3", "unified": "^10.1.0"}, "bin": {"ttx": "./bin/ttx.js"}}