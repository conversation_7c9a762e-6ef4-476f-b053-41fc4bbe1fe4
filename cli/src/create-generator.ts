import { SummaryPartShape } from '@topwrite/core/src';
import JsonGenerator from './generator/json';
import FS from './fs';
import { Book, Config, Summary } from '@topwrite/core';
import tmp from 'tmp';
import HtmlGenerator from './generator/html';
import { Options } from './generator';
import EbookGenerator from './generator/ebook';
import fse from 'fs-extra';
import path from 'path';
import { getConfig } from './config';
import logger from './logger';

const generators = {
    json: JsonGenerator,
    html: HtmlGenerator,
    ebook: EbookGenerator
};

interface BuildConfig {
    id?: string;
    article?: string;
    metadata?: Record<string, any>;
    plugins?: {
        host?: string
        preset?: Record<string, object>
    };
    lfs?: string;
    poweredBy?: {
        name: string
        link: string
    };
    asset?: 'local' | 'cdn';
}

export default async function createGenerator(dir: string, format: keyof typeof generators, dest: string, version: string, sha?: string) {

    if (typeof generators[format] === 'undefined') {
        throw new Error('unsupported format: ' + format);
    }

    const generator = generators[format];

    const fs = new FS(dir);

    const opt: BuildConfig = await fs.readAsObject('ttxconfig.json');

    logger.debug('read options form ttxconfig.json');
    logger.debug(opt);

    const config = Config.createFromObject(await fs.readAsObject(Config.file));

    const root = fs.resolve(config.getValue('root', ''));

    const tmpDir = tmp.dirSync({ unsafeCleanup: true }).name;
    const tmpFS = new FS(tmpDir);

    await fse.copy(root, tmpDir);

    await fse.copy(path.resolve(__dirname, '../templates/.bookignore'), tmpFS.resolve('.bookignore'));

    const options: Options = {
        plugins: {
            host: getConfig('plugins_host'),
            preset: {},
            ...opt.plugins
        },
        poweredBy: {
            name: '顶想云',
            link: 'https://www.topthink.com',
        },
        asset: opt.asset || 'local'
    };

    if (opt.poweredBy !== undefined) {
        options.poweredBy = opt.poweredBy;
    }

    if (opt.lfs) {
        options.lfs = {
            url: opt.lfs,
            files: {}
        };
    }

    const id = opt.id || 'unknown';
    const metadata = opt.metadata || {};

    let summaryData: string | SummaryPartShape[];
    if (tmpFS.exist(Summary.file.json)) {
        summaryData = await tmpFS.readAsObject(Summary.file.json, []);
    } else {
        summaryData = await tmpFS.readAsString(Summary.file.markdown);
    }

    const summary = Summary.create(summaryData);
    const book = new Book(id, config, summary, metadata, sha);

    const gen = new generator(book, fs, tmpFS, options, dest, version);

    if (opt.article && gen instanceof EbookGenerator) {
        //打包单个章节
        gen.setArticle(opt.article);
    }

    return gen;
}
