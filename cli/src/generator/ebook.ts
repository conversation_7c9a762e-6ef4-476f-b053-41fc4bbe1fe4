import HtmlGenerator from './html';
import path from 'path';
import { lookpath } from 'lookpath';
import spawn from 'cross-spawn';
import dargs from 'dargs';
import { pollUntil } from '../utils/poll-until';
import logger from '../logger';
import ResourceLoader from '../utils/resource-loader';
import { JSDOM, VirtualConsole } from 'jsdom';
import { File, md5, request, SummaryArticle } from '@topwrite/core';
import { parse } from 'url';
import stream from 'stream';
import mime from 'mime-types';
import pMap from 'p-map';
import ejs from 'ejs';

function escapeRegExp(string: string) {
    return string.replace(/[.*+?^${}()|[\]\\]/g, '\\$&');
}

export default class EbookGenerator extends HtmlGenerator {

    protected format = 'ebook';

    protected article: SummaryArticle | undefined;

    setArticle(ref: string) {
        const article = this.book.summary.getArticle((article) => article.getRef() === ref);
        if (!article) {
            throw new Error(`article ${ref} not found`);
        }
        this.article = article;
    }

    protected async initPlugins() {
        this.book.config.setValue('theme', 'ebook');
        this.options.plugins.preset['theme-ebook'] = {};
        await super.initPlugins();
    }

    protected async generateArticles() {
        if (this.article) {
            await this.generateArticle(this.article);
        } else {
            await super.generateArticles();
        }
    }

    protected async createDOM(html: string, file: File) {
        const resourceLoader = new ResourceLoader();
        const virtualConsole = new VirtualConsole();
        virtualConsole.on('debug', (message) => {
            logger.debug(message);
        });
        virtualConsole.on('error', (message) => {
            logger.error(message);
        });

        virtualConsole.on('jsdomError', (message) => {
            logger.error(message);
        });

        let loaded = false, loadingScript = 0;

        const dom = new JSDOM(html, {
            virtualConsole,
            url: `file:///${this.tmpFS.resolve(file.path)}`,//TODO about:blank
            runScripts: 'dangerously',
            resources: resourceLoader,
            pretendToBeVisual: true,
            beforeParse(window) {
                window.SC_DISABLE_SPEEDY = true;

                const { document } = window;

                const config = { attributes: true, childList: true, subtree: true };

                window.addEventListener('load', () => {
                    const observer = new window.MutationObserver(function(mutationsList) {
                        for (const mutation of mutationsList) {
                            if (mutation.type === 'childList') {
                                mutation.addedNodes.forEach(node => {
                                    if (node.nodeName === 'SCRIPT') {
                                        ++loadingScript;
                                        const script = node as HTMLScriptElement;
                                        script.addEventListener('load', () => {
                                            --loadingScript;
                                        });
                                    }
                                });
                            }
                        }
                    });

                    observer.observe(document.head, config);
                });

                window.addEventListener('pageLoaded', () => {
                    loaded = true;
                });
            }
        });

        await pollUntil<string>(() => {
            if (!loaded || loadingScript > 0) {
                throw new Error('render html timeout');
            }
        }, {
            stopAfter: 5 * 1000,
            tryEvery: 100
        });

        return dom;
    }

    protected async renderFile(file: File, title?: string) {
        const html = await super.renderFile(file, title);

        const dom = await this.createDOM(html, file);

        await pMap(Array.from(dom.window.document.images), async (image, index) => {
            if (image.src) {
                const url = parse(image.src);
                if (url.protocol?.startsWith('http')) {
                    try {
                        logger.info(`fetch image#${index}: ${image.src}`);
                        image.src = await this.fetchImage(file, image.src);
                        logger.info(`image#${index} fetch succeed`);
                    } catch {
                        logger.warn(`image#${index} ${image.src} fetch failed`);
                    }
                }
            }
        }, {
            concurrency: 3
        });

        //移除脚本
        for (const script of Array.from(dom.window.document.scripts)) {
            script.remove();
        }

        return dom.serialize();
    }

    protected async fetchImage(file: File, url: string) {

        //下载图片
        const response = await request.get<stream.Readable>(url, {
            responseType: 'stream',
            timeout: 5000,
            headers: {
                'User-Agent': `TopWriteBuilder/${this.version}`
            }
        });

        let filename = `_images/${md5(url)}`;

        const ext = mime.extension(response.headers['content-type']);
        if (ext) {
            filename += `.${ext}`;
        }

        const writer = this.tmpFS.createWriteStream(filename);

        return new Promise<string>((resolve, reject) => {
            response.data.pipe(writer);
            writer.on('error', err => {
                writer.close();
                reject(err);
            });
            writer.on('close', () => {
                resolve(file.relative(filename));
            });
        });
    }

    protected async getConvertOptions(format: string) {
        const { config } = this.book;

        const options: Record<string, boolean | string | number | string[]> = {
            'title': config.getValue('title'),
            'book-producer': 'TopWrite',
            'publisher': 'TopWrite',
            'chapter': 'descendant-or-self::*[contains(concat(\' \', normalize-space(@class), \' \'), \'book-chapter\')]',
            'level1-toc': 'descendant-or-self::*[contains(concat(\' \', normalize-space(@class), \' \'), \'book-level1-toc\')]',
            'level2-toc': 'descendant-or-self::*[contains(concat(\' \', normalize-space(@class), \' \'), \'book-level2-toc\')]',
            'level3-toc': 'descendant-or-self::*[contains(concat(\' \', normalize-space(@class), \' \'), \'book-level3-toc\')]',
            'max-toc-links': '0',
            'max-levels': '1',
            'no-chapters-in-toc': true,
            'breadth-first': true,
            'disable-font-rescaling': true
        };

        switch (format) {
            case 'docx':
                options['docx-no-toc'] = true;
                break;
            case 'epub':
                options['dont-split-on-page-breaks'] = true;
                break;
            case 'pdf':
                const data = {
                    poweredBy: this.options.poweredBy
                };

                const headerTpl = await ejs.renderFile(path.resolve(__dirname, '../templates/pdf-header.ejs'), data, { rmWhitespace: true });
                const footerTpl = await ejs.renderFile(path.resolve(__dirname, '../templates/pdf-footer.ejs'), data, { rmWhitespace: true });

                options['chapter-mark'] = config.getValue(['pdf', 'chapterMark']);
                options['page-breaks-before'] = config.getValue(['pdf', 'pageBreaksBefore']);
                options['pdf-page-margin-left'] = config.getValue(['pdf', 'margin', 'left']);
                options['pdf-page-margin-right'] = config.getValue(['pdf', 'margin', 'right']);
                options['pdf-page-margin-top'] = config.getValue(['pdf', 'margin', 'top']);
                options['pdf-page-margin-bottom'] = config.getValue(['pdf', 'margin', 'bottom']);
                options['pdf-serif-family'] = config.getValue(['pdf', 'fontFamily']);
                options['paper-size'] = config.getValue(['pdf', 'paperSize']);
                options['pdf-custom-size'] = config.getValue(['pdf', 'customSize']);
                options['unit'] = 'millimeter';
                options['pdf-page-numbers'] = config.getValue(['pdf', 'pageNumbers']);
                options['preserve-cover-aspect-ratio'] = true;

                if (!this.article) {
                    options['pdf-header-template'] = headerTpl;
                }

                options['pdf-footer-template'] = footerTpl;
                break;
        }

        return options;
    }

    protected async packBook() {

        const format = path.extname(this.dest).substr(1);

        if (!['pdf', 'docx', 'epub'].includes(format)) {
            return super.packBook();
        }

        const command = await lookpath('ebook-convert');
        if (!command) {
            throw new Error('Install ebook-convert from Calibre: https://calibre-ebook.com');
        }

        const options = await this.getConvertOptions(format);
        const args = dargs(options);

        const input = this.article ? this.tmpFS.resolve(this.article.path) : this.tmpFS.resolve('index.html');

        args.unshift(input, this.dest);

        return new Promise<void>((resolve, reject) => {
            logger.info('convert book ...');

            const child = spawn(command, args, {
                stdio: ['inherit'],
                cwd: this.tmpFS.root
            });

            const blackWords = [
                '^Conversion options ',
                '^  \\w+: ',
                '^Blocking URL ',
                '^Ignoring CSS ',
                '^Ignoring internal ',
                '^CSSStyleDeclaration: ',
                '^CSSStylesheet: ',
                '^PropertyValue: ',
                '^CSSVariable: ',
                escapeRegExp(input),
                escapeRegExp(this.dest)
            ].map(v => `(${v})`).join('|');

            const reg = new RegExp(`(${blackWords})`);

            child.stdout?.on('data', (data: Buffer) => {
                const message = data.toString().replace(/[\r\n]*$/, '');

                message.split('\n')
                       .forEach((line) => {
                           if (line) {
                               const message = line.trim();
                               if (line.match(reg)) {
                                   logger.debug({ message, type: 'log' });
                               } else {
                                   logger.log(message);
                               }
                           }
                       });
            });

            child.stderr?.on('data', (data: Buffer) => {
                logger.error(data.toString().trimEnd());
            });

            child.on('close', (code) => {
                if (code !== 0) {
                    reject('Convert failed');
                } else {
                    resolve();
                }
            });

        });

    }
}
