import { FormValidation, WidgetProps } from '@topthink/json-form';
import {
    SummaryArticle,
    SummaryArticleShape,
    SummaryPart,
    SummaryPartShape,
    useActions,
    useEventTarget,
    useModel
} from '@topwrite/common';
import { useCallback } from 'react';
import { DropdownProps } from 'react-bootstrap';
import Modal from '../../components/modal';
import { PromptOptions } from '../../components/modal/prompt';
import useFormatMessage from '../../lib/use-format-message';
import ExportModal from '../export/modal';
import ImportModal from '../import/modal';

export default function useMenuItemClick(item?: SummaryArticle | SummaryPart): DropdownProps['onSelect'] {
    const t = useFormatMessage();
    const [{ summary }, { updateSummary }] = useModel('book');
    const { setCurrent, removeFile, renameFile } = useActions('workspace');
    const et = useEventTarget();

    return useCallback(async (key, e) => {
        e.preventDefault();

        const validate = (filename: string, errors: FormValidation) => {
            if (!filename.match('^[^\\\\:*?"<>|]+?\\.md$|^$')) {
                errors.ref.addError('仅支持以.md结尾的合规文件名');
            } else if (['index.md', 'README.md', 'SUMMARY.md'].includes(filename)) {
                errors.ref.addError(`${filename}为系统保留文件名`);
            } else if (!!summary.getArticle(article => article.ref === filename && article !== item)) {
                errors.ref.addError('该文件已被其他章节关联');
            }
        };

        let result;
        switch (true) {
            case key === 'new_part' :
                const partOptions: PromptOptions<SummaryPartShape> = {
                    title: t('catalog.new_part'),
                    schema: {
                        type: 'object',
                        required: ['title'],
                        properties: {
                            title: {
                                title: t('catalog.part_name'),
                                type: 'string',
                            },
                        }
                    },
                    uiSchema: {
                        title: {
                            'ui:autofocus': true
                        }
                    }
                };

                et.dispatchEvent(new CustomEvent('beforeCreatePart', { detail: partOptions }));

                result = await Modal.prompt(partOptions);

                if (result) {
                    const { title, ...metadata } = result;

                    updateSummary(summary => summary.insertPart({ title, metadata }));
                }
                break;
            case key === 'new_article':
                const articleOptions: PromptOptions<SummaryArticleShape> = {
                    title: t('catalog.new_article'),
                    schema: {
                        type: 'object',
                        required: ['title'],
                        properties: {
                            title: {
                                title: t('catalog.article_title'),
                                type: 'string',
                            },
                            ref: {
                                title: t('catalog.article_ref'),
                                type: 'string',
                                maxLength: 255,
                            }
                        }
                    },
                    validate: (formData, errors) => {
                        if (formData.ref) {
                            validate(formData.ref, errors);
                        }
                        return errors;
                    },
                    uiSchema: {
                        title: {
                            'ui:autofocus': true,
                            'ui:options': {
                                widget: function({ onChange, registry, ...props }: WidgetProps) {
                                    const { widgets: { TextWidget } } = registry;

                                    const handleChange: WidgetProps['onChange'] = (value) => {
                                        if (value) {
                                            const newValue = value
                                            .replace(/\//g, '-')
                                            .replace(/['"\\\/\b\f\n\r\t\s]/g, '')
                                            .replace(/[@#$%^&*{}():"<>?!]/g, '') + '.md';

                                            setTimeout(() => {
                                                let refInput = document.getElementById('root_ref') as HTMLInputElement;
                                                if (refInput) {
                                                    let lastValue = refInput.value;
                                                    refInput.value = newValue;
                                                    let event = new Event('input', { bubbles: true });

                                                    // hack React16 内部定义了descriptor拦截value，此处重置状态
                                                    let tracker = (refInput as any)._valueTracker;

                                                    if (tracker) {
                                                        tracker.setValue(lastValue);
                                                    }

                                                    refInput.dispatchEvent(event);
                                                }
                                            });
                                        }
                                        return onChange(value);
                                    };
                                    return <TextWidget
                                        {...props}
                                        registry={registry}
                                        onChange={handleChange}
                                    />;
                                }
                            }
                        }
                    },
                };

                et.dispatchEvent(new CustomEvent('beforeCreateArticle', { detail: articleOptions }));

                result = await Modal.prompt(articleOptions);

                if (result) {
                    const { title, ref, ...metadata } = result;
                    const level = item ? item.createChildLevel() : summary.getLastPart().createChildLevel();

                    updateSummary(summary => summary.insertArticle({ title, ref, metadata }, level));
                    if (result.ref) {
                        setCurrent(result.ref);
                    }
                }
                break;
            case key === 'import_article':
                result = await Modal.show<SummaryArticleShape[]>(ImportModal);
                if (result) {
                    const level = item ? item.createChildLevel() : summary.getLastPart().createChildLevel();
                    updateSummary(summary => {
                        result.reverse().forEach(result => summary.insertArticle(result, level));
                    });
                }
                break;
            case key === 'export':
                if (item instanceof SummaryArticle) {
                    Modal.show(ExportModal, {
                        article: item.ref,
                        title: item.title
                    });
                }
                break;
            case key === 'edit':
                if (item instanceof SummaryPart) {
                    const options: PromptOptions<SummaryPartShape> = {
                        title: t('catalog.edit_part'),
                        schema: {
                            type: 'object',
                            required: ['title'],
                            properties: {
                                title: {
                                    title: t('catalog.part_name'),
                                    type: 'string',
                                }
                            }
                        },
                        uiSchema: {
                            title: {
                                'ui:autofocus': true
                            }
                        },
                        formData: {
                            ...item.metadata,
                            title: item.title,
                        },
                    };

                    et.dispatchEvent(new CustomEvent('beforeUpdatePart', { detail: options }));

                    result = await Modal.prompt(options);

                    if (result) {
                        const { title, ...metadata } = result;

                        updateSummary(summary => summary.updatePart(item.level, { title, metadata }));
                    }
                }
                if (item instanceof SummaryArticle) {
                    const options: PromptOptions<SummaryArticleShape> = {
                        title: t('catalog.edit_article'),
                        schema: {
                            type: 'object',
                            required: ['title'],
                            properties: {
                                title: {
                                    title: t('catalog.article_title'),
                                    type: 'string',
                                },
                                ref: {
                                    title: t('catalog.article_ref'),
                                    type: 'string',
                                    maxLength: 255
                                }
                            }
                        },
                        validate: (formData, errors) => {
                            if (formData.ref) {
                                validate(formData.ref, errors);
                            }
                            return errors;
                        },
                        uiSchema: {
                            title: {
                                'ui:autofocus': true
                            }
                        },
                        formData: {
                            ...item.metadata,
                            title: item.title,
                            ref: item.ref
                        },
                    };

                    et.dispatchEvent(new CustomEvent('beforeUpdateArticle', { detail: options }));

                    result = await Modal.prompt(options);

                    if (result) {
                        const { title, ref, ...metadata } = result;

                        updateSummary(summary => summary.updateArticle(item.level, { title, ref, metadata }));

                        if (result.ref !== item.ref && result.ref && item.ref) {
                            //重命名文件
                            renameFile(item.ref, result.ref);
                        }
                    }
                }
                break;
            case key === 'delete':
                result = await Modal.confirm({ message: t('confirm.delete') });
                if (result && item) {

                    //删除子目录
                    let wantToDeletePaths = new Set<string>([]);

                    if (item instanceof SummaryArticle) {
                        updateSummary(summary => summary.removeArticle(item.level));
                        //删除文件
                        if (item.ref) {
                            wantToDeletePaths.add(item.ref);
                        }
                    }

                    if (item instanceof SummaryPart) {
                        updateSummary(summary => summary.removePart(item.level));
                    }

                    SummaryArticle.findArticle(item, (article) => {
                        if (article.ref) {
                            wantToDeletePaths.add(article.ref);
                        }
                        return false;
                    });

                    for (let file of wantToDeletePaths) {
                        removeFile(file);
                    }
                }
                break;
        }

    }, [summary, updateSummary, setCurrent, removeFile, renameFile, item]);
}
