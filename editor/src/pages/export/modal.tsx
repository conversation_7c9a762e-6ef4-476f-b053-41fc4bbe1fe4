import { request, styled, useOptions } from '@topwrite/common';
import { nanoid } from 'nanoid';
import { ReactNode, useCallback, useEffect, useState } from 'react';
import { BsCheckCircle, BsExclamationCircle } from 'react-icons/bs';
import Loader from '../../components/loader';
import Modal, { MessageProps } from '../../components/modal';
import Toast from '../../components/toast';
import { ReactComponent as PdfIcon } from '../../images/ebook/pdf.svg';
import { ReactComponent as WordIcon } from '../../images/ebook/word.svg';
import { socket } from '../../lib/socket';
import useFormatMessage from '../../lib/use-format-message';

interface TraceEvent {
    type: 'trace';
    message: string;
}

interface ErrorEvent {
    type: 'error';
    error: string;
}

interface SucceedEvent {
    type: 'succeed';
}

const EBooks = {
    pdf: {
        title: 'Pdf',
        icon: <PdfIcon />
    },
    word: {
        title: 'Word',
        icon: <WordIcon />
    },
};

export default function ExportModal({ state, message: { article, title } }: MessageProps<{
    article: string
    title: string
}>) {
    const t = useFormatMessage();
    const [loading, setLoading] = useState(false);
    const [id, setId] = useState<string>();
    const [error, setError] = useState<string>();
    const [message, setMessage] = useState<string>();
    const { export: url } = useOptions();
    const [succeed, setSucceed] = useState(false);

    useEffect(() => {
        const event = `export.${id}`;
        const listener = (data: TraceEvent | ErrorEvent | SucceedEvent) => {
            switch (data.type) {
                case 'error':
                    setError(data.error);
                    break;
                case 'succeed':
                    setLoading(false);
                    setSucceed(true);
                    break;
                case 'trace':
                    setMessage(data.message.trim().split('\n').at(-1));
                    break;
            }
        };

        socket.on(event, listener);
        return () => {
            socket.off(event, listener);
        };
    }, [id]);

    const onExport = useCallback(async (name: string) => {
        setLoading(true);
        setMessage(undefined);
        const id = nanoid();
        setId(id);
        try {
            await request({
                url: `${url}/${id}`,
                method: 'POST',
                data: {
                    type: name,
                    article,
                },
            });
        } catch (e: any) {
            Toast.error(e.message);
            setLoading(false);
        }
    }, [article]);

    let children: ReactNode;
    if (succeed) {
        children = <Item className={'w-100'}>
            <BsCheckCircle size={64} className={'text-success'} />
            <h6 className={'mb-3'}>{t('catalog.export_succeed')}</h6>
            <p className={'mb-0'}>
                <a className={'btn btn-primary'} href={`${url}/${id}?download=${title}`} target='_blank'>点击下载</a>
            </p>
        </Item>;
    } else if (error) {
        children = <Item className={'w-100'}>
            <BsExclamationCircle size={64} className={'text-danger'} />
            <h6 className={'mb-3'}>{t('catalog.export_failed')}</h6>
            <p className={'mb-0 text-break'}>{error}</p>
        </Item>;
    } else {
        children = <>
            <Loader loading={loading}>{message}</Loader>
            <Info>{t('catalog.export_desc')}</Info>
            <FileTypeSelector>
                {Object.entries(EBooks).map(([name, { title, icon }]) => {
                    return <Item role={'button'} key={name} onClick={() => {
                        onExport(name);
                    }}>
                        {icon}
                        <h6>{title}</h6>
                    </Item>;
                })}
            </FileTypeSelector>
        </>;
    }

    return <Modal
        {...state}
        backdrop={'static'}
        title={t('catalog.export')}
        footer={null}
    >
        {children}
    </Modal>;
}

const Item = styled.div`
    width: 25%;
    display: flex;
    flex-direction: column;
    align-items: center;

    svg {
        padding: .5rem;
        color: var(--bs-primary);
    }

    h6 {
        margin-top: 4px;
        margin-bottom: 0;
        font-weight: normal;
        line-height: 24px;
    }
`;

const FileTypeSelector = styled.div`
    display: flex;
`;

const Info = styled.div`
    margin-bottom: 1rem;
    color: rgb(var(--bs-secondary-rgb));
`;
