import { styled } from '@topwrite/common';
import dayjs from 'dayjs';
import { Dictionary } from 'lodash';
import { Nav } from 'react-bootstrap';
import { GoGitCommit } from 'react-icons/go';
import { Release, ReleaseLog } from 'repo';
import Status from './status';
import { useTypes } from './utils';

interface Props {
    release: Release;
    logs: Dictionary<ReleaseLog>;
}

export default function Sidebar({ release, logs }: Props) {

    const date = dayjs(release.create_time);

    const items = useTypes(release, logs);

    return <Container>
        <Meta>
            <Sha>
                <GoGitCommit className='me-1' />
                {release.sha.substr(0, 7)}
            </Sha>
            <Author>
                <span className='me-1' title={date.format()}>
                    {date.fromNow()}
                </span>
                by {release.user.name}
            </Author>
        </Meta>
        <Header>
            <Status status={logs[release.main]?.status_text ?? 'unknown'} />
            <Message>
                {release.message || 'No Message'}
            </Message>
        </Header>
        <Nav variant='pills' className='flex-column'>
            {items.map(([k, v]) => {
                return <Nav.Item key={k}>
                    <Nav.Link eventKey={k}>
                        <Status status={logs[k]?.status_text} size='sm' />{v}
                    </Nav.Link>
                </Nav.Item>;
            })}
        </Nav>
    </Container>;
}

const Author = styled.div`

`;

const Sha = styled.div`
    flex: auto;
    display: flex;
    align-items: center;
`;

const Meta = styled.div`
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    height: 20px;
    margin-bottom: 8px;
    color: var(--ttw-secondary-color);
    padding: 8px;
`;

const Header = styled.div`
    padding: 8px;
    margin-bottom: 16px;
    display: flex;
    align-items: center;
`;

const Message = styled.div`
    margin-left: 14px;
    flex: auto;
    white-space: pre;
`;

const Container = styled.div`
    width: 290px;
    background: var(--ttw-foreground);
    padding: 12px 8px;
    border-left: 1px solid var(--ttw-border-color);
    flex-shrink: 0;

    .nav-link {
        color: var(--ttw-color);
        align-items: center;
        display: flex;

        .bi, .spinner-grow {
            margin-right: 1rem;
        }

        &.active {
            color: var(--ttw-color);
            background-color: var(--ttw-file-active-background);
        }
    }
`;
