import { useSelector } from '@topwrite/common';
import { Dropdown } from 'react-bootstrap';
import { BsDownload } from 'react-icons/bs';
import { Release } from 'repo';
import ContextMenu, { MenuItem } from '../../components/context-menu';
import Modal from '../../components/modal';
import Toast from '../../components/toast';
import Tooltip from '../../components/tooltip';
import { socket } from '../../lib/socket';
import useFormatMessage from '../../lib/use-format-message';
import Status from './status';
import { useLogs, useTypes } from './utils';

export default function Download({ release }: { release: Release }) {
    const t = useFormatMessage();
    const [logs] = useLogs(release);
    const items = useTypes(release, logs);
    const { download, release: canRelease } = useSelector('options');

    return <Dropdown onSelect={async (eventKey) => {
        if (eventKey) {
            switch (logs[eventKey]?.status) {
                case 1:
                    const a = document.createElement('a');
                    a.target = '_blank';
                    a.href = `${download}?type=${eventKey}&sha=${release.sha}`;
                    a.click();
                    break;
                case -1:
                case undefined:
                    if (canRelease) {
                        if (await Modal.confirm({
                            title: t('release.pack'),
                            message: '此格式尚未成功打包，是否开始打包？',
                        })) {
                            await socket.retryRelease(release.id, eventKey);
                        }
                        break;
                    }
                default:
                    Toast.error('此格式尚未打包或正在打包中');
                    break;

            }
        }
    }}>
        <Tooltip tooltip={t('release.download')}>
            <Dropdown.Toggle variant='light'>
                <BsDownload />
            </Dropdown.Toggle>
        </Tooltip>
        <ContextMenu>
            {items.map(([k, v]) => {
                return <MenuItem icon={<Status status={logs[k]?.status_text} size={'sm'} />} key={k} eventKey={k}>
                    {v}
                </MenuItem>;
            })}
        </ContextMenu>
    </Dropdown>;
}
