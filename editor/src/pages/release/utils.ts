import { Dictionary, keyBy } from 'lodash';
import { Dispatch, SetStateAction, useEffect, useState } from 'react';
import { Release, ReleaseLog } from 'repo';
import { socket } from '../../lib/socket';

export const TYPES = {
    html: 'HTML',
    pdf: 'PDF',
    epub: 'Epub',
    word: 'Word',
    json: 'Raw'
};

export const useLogs = function(release: Release): [Dictionary<ReleaseLog>, Dispatch<SetStateAction<Dictionary<ReleaseLog>>>] {
    const [logs, setLogs] = useState(() => keyBy(release.logs, 'type'));


    useEffect(() => {
        const updateListener = (type: string, log: ReleaseLog) => {
            setLogs(logs => ({
                ...logs,
                [type]: {
                    ...logs[type],
                    ...log
                }
            }));
        };

        const updateEvent = `release.${release.id}.update`;
        socket.on(updateEvent, updateListener);

        const traceListener = (type: string, trace: string) => {
            setLogs(logs => ({
                ...logs,
                [type]: {
                    ...logs[type],
                    trace: logs[type].trace + trace
                }
            }));
        };

        const traceEvent = `release.${release.id}.trace`;
        socket.on(traceEvent, traceListener);

        return () => {
            socket.off(updateEvent, updateListener);
            socket.off(traceEvent, traceListener);
        };
    }, [release]);

    return [logs, setLogs];
};

export const useTypes = function(release, logs: Dictionary<ReleaseLog>) {
    return Object.entries(TYPES).sort(([k1], [k2]) => {
        if (k1 === release.main) {
            return -1;
        }
        if (k2 === release.main) {
            return 1;
        }
        if (!logs[k1] === !logs[k2]) {
            return 0;
        }
        return logs[k1] ? -1 : 1;
    });
};
