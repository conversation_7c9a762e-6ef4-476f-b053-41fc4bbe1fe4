import { css, styled, useActions, useAsync, useSelector } from '@topwrite/common';
import dayjs from 'dayjs';
import { MouseEventHandler, useEffect, useRef, useState } from 'react';
import { GoGitCommit, GoPlus } from 'react-icons/go';
import { Release, ReleaseLog } from 'repo';
import Button from '../../components/button';
import { PaneHeader } from '../../components/pane';
import ScrollList from '../../components/scroll-list';
import Tooltip from '../../components/tooltip';
import { socket } from '../../lib/socket';
import useFormatMessage from '../../lib/use-format-message';
import Detail from './detail';
import Download from './download';
import ReleaseModal from './modal';
import Status from './status';

interface ReleaseItemProps {
    release: Release,
    active: boolean,
    onClick: MouseEventHandler<HTMLDivElement>
}

function ReleaseItem({ release: defaultRelease, active, onClick }: ReleaseItemProps) {

    const [release, setRelease] = useState(defaultRelease);

    useEffect(() => {
        const listener = (type: string, log: ReleaseLog) => {
            if (type === release.main) {
                setRelease({
                    ...release,
                    status: log.status,
                    status_text: log.status_text
                });
            }
        };
        const event = `release.${release.id}.update`;
        socket.on(event, listener);
        return () => {
            socket.off(event, listener);
        };
    }, [release]);

    const date = dayjs(release.create_time);

    return <ItemContainer
        key={release.id}
        active={active}
        onClick={onClick}
    >
        <ItemHeader>
            <div className='flex-fill'>{release.title || 'No Message'}</div>
            <Status status={release.status_text} size='sm' />
        </ItemHeader>
        <ItemMeta>
            <span className='flex-fill'>
                <GoGitCommit className='me-1' />
                {release.sha.substr(0, 7)}
            </span>
            <span>
                <span className='me-1' title={date.format()}>
                    {date.fromNow()}
                </span>
                by {release.user.name}
            </span>
        </ItemMeta>
    </ItemContainer>;
}

export default function Release() {
    const t = useFormatMessage();
    const [current, setCurrent] = useState<number>();
    const { setExtra } = useActions('workspace');
    const { release } = useSelector('options');
    const { result: last, execute } = useAsync(() => socket.getLastRelease(), []);

    const handleClick = (release: Release) => {
        return () => {
            setCurrent(release.id);
            setExtra(<Detail id={release.id} />);
        };
    };

    useEffect(() => {
        const listener = function() {
            execute();
        };
        socket.on('workspace.release', listener);

        return () => {
            socket.off('workspace.release', listener);
            setCurrent(undefined);
            setExtra(null);
        };
    }, []);

    const listRef = useRef<ScrollList>(null);

    const action = <>
        {last && <Download release={last} />}
        {release ? <ReleaseModal onSuccess={() => {
            if (listRef.current) {
                listRef.current.refresh();
            }
        }} /> : <Tooltip tooltip={t('release.denied')}>
            <Button variant='light'><GoPlus /></Button>
        </Tooltip>}</>;

    return <>
        <PaneHeader action={action}>
            {t('active-bar.release')}
        </PaneHeader>
        <Container>
            <ScrollList<Release>
                ref={listRef}
                fetchData={async (result) => {
                    const list = await socket.getReleaseList(result ? result.page + 1 : 1);
                    return {
                        data: list.data,
                        hasMore: list.current_page < list.last_page,
                        page: list.current_page
                    };
                }}
                renderItem={release => <ReleaseItem
                    key={release.id}
                    release={release}
                    active={current === release.id}
                    onClick={handleClick(release)}
                />}
            />
        </Container>
    </>;
}

const Container = styled.div`
    flex: auto;
    overflow: hidden;
    position: relative;
`;

const ItemMeta = styled.div`
    font-size: 12px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: flex;
    align-items: center;
    height: 20px;
    margin-top: 8px;
    color: var(--ttw-secondary-color);
`;

const ItemHeader = styled.div`
    display: flex;
    align-items: center;
    color: var(--ttw-color);
`;

const ItemContainer = styled.div<{ active: boolean }>`
    border-bottom: 1px solid var(--ttw-border-color);
    padding: 8px;
    cursor: pointer;

    &:hover {
        background-color: var(--ttw-file-hover-background);
    }

    ${props => props.active && css`
        background-color: var(--ttw-file-active-background)
    `};

`;
