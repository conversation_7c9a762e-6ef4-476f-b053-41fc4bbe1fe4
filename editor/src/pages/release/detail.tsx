import { styled, useAsync } from '@topwrite/common';
import { useCallback, useMemo, useState } from 'react';
import { Tab } from 'react-bootstrap';
import { Release } from 'repo';
import Loader from '../../components/loader';
import { socket } from '../../lib/socket';
import Log from './log';
import Sidebar from './sidebar';
import { useLogs } from './utils';

interface LogsProps {
    release: Release;
}

function Logs({ release }: LogsProps) {

    const [logs, setLogs] = useLogs(release);

    const [type, setType] = useState<string>(release.main);

    const clearTrace = useCallback(() => {
        setLogs(logs => ({
            ...logs,
            [type]: {
                ...logs[type],
                trace: ''
            }
        }));
    }, [type]);

    const log = useMemo(() => {
        return logs[type] ?? {
            release_id: release.id,
            type,
            status: -2,
            status_text: 'unknown',
            trace: '',
        };
    }, [type, logs, release]);

    return <Tab.Container id='release-detail' activeKey={type} onSelect={(eventKey) => {
        if (eventKey) {
            setType(eventKey);
        }
    }}>
        <Container>
            <Log log={log} clearTrace={clearTrace} release={release} />
            <Sidebar logs={logs} release={release} />
        </Container>
    </Tab.Container>;
}

const Container = styled.div`
    display: flex;
    height: 100%;
`;

interface DetailProps {
    id: number;
}

export default function Detail({ id }: DetailProps) {
    const { result: release, loading } = useAsync(() => socket.getReleaseDetail(id), [id]);

    if (!release) {
        return <Loader loading={loading} />;
    }

    return <Logs release={release} />;
}
