import { css, styled } from '@topwrite/common';
import { Change, diffLines } from 'diff';
import { ReactNode, useState } from 'react';
import { Spinner } from 'react-bootstrap';
import { BsCaretDownFill, BsCaretUpFill, BsCheckCircleFill, BsDashCircleFill, BsXCircleFill } from 'react-icons/bs';
import useFormatMessage from '../../lib/use-format-message';
import Content, { ContentType } from './content';

export interface ToolMessage {
    name: string;
    title: string;
    arguments: string;
    response?: string;
    error?: boolean;
    content?: ContentType;
}

interface Props {
    tool: ToolMessage;
    loading?: boolean;
}

const parseArguments = (tool: ToolMessage) => {
    try {
        return JSON.parse(tool.arguments);
    } catch (e) {
        return null;
    }
};

export default function Tool({ tool, loading }: Props) {
    const t = useFormatMessage();
    const hasResponse = 'response' in tool;
    const isCancelled = !hasResponse && !loading;

    const args = parseArguments(tool);

    let defaultShow = false;
    let filename = null;

    let response: ReactNode = <div className='border-top p-2 d-flex flex-column gap-2'>
        <ToolSection className='border rounded'>
            <div className='d-flex align-items-center p-1 px-2 fs-7 gap-2'>
                <span className='text-muted'>{t('assistant.tool.parameters')}</span>
            </div>
            <Response className='border-top p-2'>
                {tool.arguments}
            </Response>
        </ToolSection>
        {!isCancelled && <ToolSection className='border rounded'>
            <div className='d-flex align-items-center p-1 px-2 fs-7 gap-2'>
                <span className='text-muted'>{t('assistant.tool.response')}</span>
            </div>
            <Response className='border-top p-2'>
                {tool.response || 'None'}
            </Response>
        </ToolSection>}
    </div>;

    if (args) {
        switch (tool.name) {
            case 'view':
            case 'download':
            case 'delete':
                filename = args.path;
                response = null;
                break;
            case 'rename':
                filename = args.old_path;
                response = null;
                break;
            case 'create':
            case 'insert':
            case 'str_replace':
                filename = args.path;
                if (isCancelled || tool.error) {
                    response = null;
                } else {
                    let changes: Change[];
                    switch (tool.name) {
                        case 'insert':
                            changes = diffLines('', args.str);
                            break;
                        case 'create':
                            changes = diffLines('', args.content);
                            break;
                        case'str_replace':
                            changes = diffLines(args.old_str, args.new_str);
                            break;
                    }

                    response = <div className='border-top p-2 d-flex flex-column gap-2'>
                        <Changes className='border rounded'>
                            {changes.map((change, index) => {
                                return <Line key={index} $added={change.added} $removed={change.removed}>{change.value}</Line>;
                            })}
                        </Changes>
                    </div>;
                }
                break;
            case 'todo_write':
                defaultShow = true;
                tool.title = '任务列表';
                console.log(args);
                response = <div className='border-top p-2 d-flex flex-column gap-2'>
                    {args.todos.map(todo => {
                        //todo.status 
                        return <div>
                            {todo.content}
                        </div>;
                    })}
                </div>;
                break;
        }
    }

    const [show, setShow] = useState(defaultShow);

    return <>
        <div className='mb-2'>
            <ToolContainer className='rounded fs-7'>
                <div onClick={() => setShow(!show)} role='button' className='d-flex align-items-center p-1 px-2 gap-2'>
                    {isCancelled ? <BsDashCircleFill className='text-muted' /> :
                        (hasResponse ? (tool.error ? <BsXCircleFill className='text-danger' /> :
                                <BsCheckCircleFill className='text-success' />) :
                            <Spinner animation='border' variant='primary' size='sm' />)}
                    <span className={'text-nowrap'}>{tool.title}</span>
                    {filename && <span className={'text-muted text-truncate'}>{filename}</span>}
                    {response && (show ? <BsCaretUpFill className='text-muted ms-auto' /> :
                        <BsCaretDownFill className='text-muted ms-auto' />)}
                </div>
                {(show && (hasResponse || isCancelled)) && response}
            </ToolContainer>
        </div>
        {tool.content && <Content value={tool.content} />}
    </>;
}

const Response = styled.div`
    white-space: pre-wrap;
    overflow-y: auto;
    overflow-x: hidden;
    max-height: 150px;
`;

const ToolContainer = styled.div`
    background: var(--ttw-editor-background);
    box-shadow: var(--ttw-shadow);
`;

const ToolSection = styled.div`
    background: var(--ttw-priview-background);
`;

const Changes = styled.div`
    padding: 0.25rem 0;
    flex: auto;
    max-height: 200px;
    overflow-y: auto;
    white-space: pre-wrap;
    word-break: break-all;
    background: var(--ttw-priview-background);
`;

const Line = styled.div<{ $removed?: boolean, $added?: boolean }>`
    line-height: 1.6;
    padding: 0 0.25rem;
    ${props => props.$removed && css`
        background-color: var(--ttw-diff-old-background);
    `};

    ${props => props.$added && css`
        background-color: var(--ttw-diff-new-background);
    `};
`;
