import { styled } from '@topwrite/common';
import { useContext } from './context';

export default function Welcome() {
    const { send, authorized } = useContext();

    const handleRecommendClick = (text: string) => {
        send(text);
    };

    return <WelcomeScreen>
        <AssistantAvatar>
            <AvatarIcon>🤖</AvatarIcon>
            <VibeGlow />
        </AssistantAvatar>
        <WelcomeSubtitle>Very Intelligent Brain Enhancement</WelcomeSubtitle>
        <WelcomeDescription>
            用AI增强你的大脑潜能，进入最佳创作状态
        </WelcomeDescription>
        {authorized && <RecommendSection>
            <RecommendTitle>今天我能为你做什么</RecommendTitle>
            <RecommendList>
                <RecommendText onClick={() => handleRecommendClick('优化下当前章节内容')}>
                    优化下当前章节内容
                </RecommendText>
                <RecommendText onClick={() => handleRecommendClick('给当前章节内容自动配图')}>
                    给当前章节内容自动配图
                </RecommendText>
                <RecommendText onClick={() => handleRecommendClick('给文档H2标题设置一个蓝底白字的样式')}>
                    给文档H2标题设置一个蓝底白字的样式
                </RecommendText>
                <RecommendText onClick={() => handleRecommendClick('详细补充下当前章节内容')}>
                    详细补充下当前章节内容
                </RecommendText>
            </RecommendList>
        </RecommendSection>}
    </WelcomeScreen>;
}


const WelcomeScreen = styled.div`
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: safe center;
    flex: 1;
    padding: 2rem;
    color: var(--bs-body-color);
    overflow-y: auto;
`;

const AssistantAvatar = styled.div`
    position: relative;
    margin-bottom: 2.5rem;
    display: flex;
    align-items: center;
    justify-content: center;
`;

const AvatarIcon = styled.div`
    font-size: 3.5rem;
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    background: linear-gradient(135deg, #3c60ff, #5a7cff);
    border-radius: 50%;
    position: relative;
    z-index: 2;
`;

const VibeGlow = styled.div`
    position: absolute;
    width: 100px;
    height: 100px;
    background: linear-gradient(135deg, #3c60ff, #5a7cff);
    border-radius: 50%;
    opacity: 0.3;
    animation: vibeGlow 2s ease-in-out infinite alternate;

    @keyframes vibeGlow {
        0% {
            transform: scale(1);
            opacity: 0.3;
        }
        100% {
            transform: scale(1.2);
            opacity: 0.1;
        }
    }
`;

const WelcomeSubtitle = styled.div`
    font-size: 0.9rem;
    color: var(--bs-secondary);
    margin-bottom: 1rem;
    font-weight: 500;
    letter-spacing: 0.5px;
`;

const WelcomeDescription = styled.p`
    font-size: 1.1rem;
    color: var(--ttw-color);
    margin-bottom: 2rem;
    max-width: 400px;
    line-height: 1.6;
`;


const RecommendSection = styled.div`
    width: 100%;
    max-width: 500px;
    margin: 2rem 0;
`;

const RecommendTitle = styled.h3`
    font-size: 1rem;
    color: var(--bs-secondary);
    margin-bottom: 0.75rem;
`;

const RecommendList = styled.div`
    display: flex;
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
`;


const RecommendText = styled.div`
    background: rgb(243, 243, 243);
    padding: 0.5rem 1rem;
    border-radius: var(--bs-border-radius-lg);
    cursor: pointer;

    &:hover {
        color: inherit;
        background: #e2e3e5;
    }
`;
